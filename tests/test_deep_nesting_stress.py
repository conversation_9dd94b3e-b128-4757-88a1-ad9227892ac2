"""
Stress tests for deep nesting to evaluate Option E (Hybrid Streaming-Recursive) architecture.
Tests memory usage, stack overflow resistance, and STRUCT type preservation.
"""

import pytest
import duckdb
import tempfile
import json
import os


class TestDeepNestingStress:
    """Stress tests for deep JSON nesting scenarios."""

    @pytest.fixture
    def duckdb_conn(self):
        """Create DuckDB connection with streaming JSON extension loaded."""
        conn = duckdb.connect(':memory:', config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "./build/debug/streaming_json_reader.duckdb_extension";')
        return conn

    @pytest.fixture
    def temp_json_file(self):
        """Create temporary JSON file for testing."""
        fd, path = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        yield path
        os.unlink(path)

    def create_deeply_nested_json(self, depth: int, temp_file: str):
        """Create JSON with specified nesting depth."""
        # Build nested structure: level1.level2.level3...levelN.value
        nested_obj = {"value": f"deep_value_at_level_{depth}"}
        
        for i in range(depth - 1, 0, -1):
            nested_obj = {f"level{i}": nested_obj}
        
        with open(temp_file, 'w') as f:
            json.dump(nested_obj, f)

    def test_nesting_depth_5_levels(self, duckdb_conn, temp_json_file):
        """Test 5 levels of nesting - should work easily."""
        self.create_deeply_nested_json(5, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT level1.level2.level3.level4.value FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        assert len(result) == 1
        assert result[0][0] == "deep_value_at_level_5"

    def test_nesting_depth_10_levels(self, duckdb_conn, temp_json_file):
        """Test 10 levels of nesting - stress test for Option E."""
        self.create_deeply_nested_json(10, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT level1.level2.level3.level4.level5.level6.level7.level8.level9.value FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        assert len(result) == 1
        assert result[0][0] == "deep_value_at_level_10"

    def test_nesting_depth_15_levels(self, duckdb_conn, temp_json_file):
        """Test 15 levels of nesting - extreme stress test."""
        self.create_deeply_nested_json(15, temp_json_file)
        
        # This would have failed with old depth limit of 15
        result = duckdb_conn.execute(f'SELECT level1.level2.level3.level4.level5.level6.level7.level8.level9.level10.level11.level12.level13.level14.value FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        assert len(result) == 1
        assert result[0][0] == "deep_value_at_level_15"

    def test_mixed_deep_nesting_with_arrays(self, duckdb_conn, temp_json_file):
        """Test deep nesting mixed with arrays."""
        data = {
            "company": {
                "departments": {
                    "engineering": {
                        "teams": {
                            "backend": {
                                "members": [
                                    {"name": "Alice", "role": "Senior"},
                                    {"name": "Bob", "role": "Junior"}
                                ]
                            }
                        }
                    }
                }
            }
        }
        
        with open(temp_json_file, 'w') as f:
            json.dump(data, f)
        
        # Test deep STRUCT access
        result = duckdb_conn.execute(f'SELECT company.departments.engineering.teams.backend.members FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        assert len(result) == 1
        # Should be an array of STRUCTs, not a VARCHAR string
        members = result[0][0]
        assert isinstance(members, list)
        assert len(members) == 2

    def test_verify_no_varchar_fallbacks(self, duckdb_conn, temp_json_file):
        """Verify that deep nesting uses proper STRUCT types, not VARCHAR fallbacks."""
        self.create_deeply_nested_json(12, temp_json_file)
        
        # Get column types to verify STRUCT preservation
        columns = duckdb_conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Should have proper STRUCT type, not VARCHAR
        assert len(columns) == 1
        column_name, column_type, nullable, key, default, extra = columns[0]
        
        # Verify it's a STRUCT type, not VARCHAR
        assert "STRUCT" in column_type
        assert "VARCHAR" not in column_type or column_type == "VARCHAR"  # Only acceptable for string fields

    def test_schema_consistency_deep_nesting(self, duckdb_conn, temp_json_file):
        """Test that schema discovery matches insertion capabilities for deep nesting."""
        data = {
            "root": {
                "branch1": {
                    "branch2": {
                        "branch3": {
                            "leaf": "value"
                        }
                    }
                }
            }
        }
        
        with open(temp_json_file, 'w') as f:
            json.dump(data, f)
        
        # This should work without crashes or VARCHAR conversions
        result = duckdb_conn.execute(f'SELECT root.branch1.branch2.branch3.leaf FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        assert len(result) == 1
        assert result[0][0] == "value"

    @pytest.mark.skip(reason="Extreme stress test - may cause stack overflow, enable for architecture evaluation")
    def test_extreme_nesting_25_levels(self, duckdb_conn, temp_json_file):
        """Extreme stress test - 25 levels of nesting."""
        self.create_deeply_nested_json(25, temp_json_file)
        
        # This test is designed to find the practical limits of Option E
        # If this fails with stack overflow, consider migrating to Option B (Iterative)
        try:
            result = duckdb_conn.execute(f'SELECT level1.level2.level3.level4.level5.level6.level7.level8.level9.level10.level11.level12.level13.level14.level15.level16.level17.level18.level19.level20.level21.level22.level23.level24.value FROM streaming_json_reader("{temp_json_file}")').fetchall()
            
            assert len(result) == 1
            assert result[0][0] == "deep_value_at_level_25"
            print("SUCCESS: Option E handles 25 levels without issues")
            
        except Exception as e:
            print(f"ARCHITECTURE DECISION POINT: Option E failed at 25 levels: {e}")
            print("RECOMMENDATION: Consider migrating to Option B (Iterative Stack-Based)")
            raise
