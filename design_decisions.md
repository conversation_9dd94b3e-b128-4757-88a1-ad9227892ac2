# Design Decisions

## Core Architecture

### Streaming JSON Processing
Use streaming JSON parsing with struson crate instead of loading entire files into memory.
- Traditional: O(file_size) memory usage
- Streaming: O(row_size) memory usage

### Language: Rust
Implement in Rust using DuckDB's C API bindings.
- User preference for Rust development
- Memory safety for database extensions
- Strong JSON parsing ecosystem (struson)

## API Design

### Automatic Path Inference
Implement automatic JSON path inference based on query column patterns rather than explicit path parameters.
- Enables `SELECT * FROM streaming_json_reader('file.json')`
- Leverages DuckDB's projection pushdown

### Context Preservation for Nested Structures
Maintain parent object context when flattening nested arrays.
Follow DuckDB's `select a, unnest(b)` pattern where `a` values repeat for each unnested row of `b`.

### SELECT * Behavior
`SELECT *` should return exactly the same as DuckDB's default JSON reader.

### Error Handling
Fail-fast on malformed JSON rather than attempting partial recovery.
- Simpler error handling logic
- Clear failure modes for debugging

### No Hard-coded JSON Structure Assumptions
Extension should work with any JSON structure, not just specific schemas. Make it truly generic.

## Query-Driven Structure Inference

JSON structure should be inferred from query structure. For example, `unnest(x)` should assume x is a nested field and unnest it into rows. Research needed on DuckDB's extension interface capabilities.

## Memory Management

Use adaptive batching based on DuckDB's vector sizes. Prefer low memory usage when choosing between options.

## Development Workflow

- Use uv for Python package management
- Make incremental git commits
- Design for general, reusable package
- Consult user when multiple design options exist
- Organize test code in separate folders
- Document design decisions for future reference

## Implementation Status

### Schema Discovery
Use first element schema only, with query-driven column selection.
- Avoids performance penalty of full file scanning
- Users can define schema by selecting named fields
- Enables streaming without lookahead

### Type System Integration
Work towards full DuckDB JSON type compatibility. Use VARCHAR for intermediate versions.
- Should eventually match DuckDB's existing JSON reader API
- Full types including nested types is the end goal

### API Surface
Single parameter API: `streaming_json_reader(file_path)` with query-driven behavior.
- `SELECT *` should match DuckDB's `read_json_auto()` behavior exactly
- Context-preserving flattening via query structure
- Query planner integration determines behavior

## Current Status

### Working Features
- Projection pushdown (skips unrequested fields)
- Error handling for malformed JSON
- Basic JSON parsing and STRUCT creation
- String data types

### Critical Issues (Resolved)
- ✅ STRUCT-within-STRUCT crashes fixed using proper `struct_vector_child()` API
- ✅ Deep nested structures (3+ levels) now working with recursive STRUCT handling
- ✅ Empty JSON objects handled with fallback "json" VARCHAR column
- ✅ Numbers properly stored as DOUBLE type with direct memory access
- ✅ STRUCT field values use correct data types
- ✅ Array processing works for STRUCT arrays
- ✅ Projection pushdown optimization working for both simple and STRUCT fields

### Known Limitations

#### Empty Object Handling
**Current Approach**: Empty JSON objects `{}` are handled by creating a single VARCHAR column named "json" containing the string "{}"

**Built-in Behavior**: DuckDB's `read_json_auto()` returns `MAP(VARCHAR, JSON)` type with actual empty map value

**Rationale for Current Approach**:
- DuckDB Rust API lacks high-level MAP type creation methods
- JsonValueType enum doesn't include MAP variant
- No MapVector equivalent in current Rust wrapper
- VARCHAR approach prevents crashes and satisfies DuckDB's "at least one column" requirement

**Future Enhancement**: Implement proper MAP type support when:
- DuckDB Rust API exposes MAP vector operations
- Extension type system is extended to support MAP types
- Core STRUCT functionality is fully robust

**Technical Debt**: This is a temporary workaround, not a design limitation

### Critical Design Constraints

#### STRUCT Type Integrity (NEVER VIOLATE)
**Forbidden Patterns**:
- ❌ Converting JSON objects to JSON strings for insertion into STRUCT fields
- ❌ Using VARCHAR as a fallback for complex nested structures
- ❌ Compromising the STRUCT type system for convenience
- ❌ Any VARCHAR conversion for STRUCT data (breaks core design principle)

**Required Patterns**:
- ✅ Use proper DuckDB STRUCT types and recursive vector handling
- ✅ Use `struct_vector_child()` for nested STRUCT access
- ✅ Implement recursive STRUCT insertion without depth limits
- ✅ Maintain schema-insertion type consistency

**Acceptable VARCHAR Uses** (Limited Exceptions):
- ✅ Empty object fallback (single "json" column as documented limitation)
- ✅ Primitive string fields that are actually strings in the JSON
- ✅ Temporary null placeholders for unimplemented features (with TODO comments)

#### Implementation Status
**Working STRUCT Features**:
- Basic STRUCT objects with primitive fields
- STRUCT-within-STRUCT (2-3 levels tested)
- STRUCT arrays with proper field access
- Recursive STRUCT vector handling using `struct_vector_child()`
- Deep nested field access (e.g., `company.departments.engineering.head`)

**Remaining Work**:
- Array flattening and complex array operations
- Arrays within deeply nested STRUCTs
- Full recursive depth handling for arbitrary nesting levels




